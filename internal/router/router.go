package router

import (
	"net/http"

	"github.com/codeandlearn1991/newsapi/internal/handler"
)

func New() *http.ServeMux {
	r := http.NewServeMux()

	r.<PERSON>("POST /news", handler.PostNews())
	r.<PERSON>("GET /news", handler.GetAllNews())
	r.<PERSON>("GET /news/{id}", handler.GetNewsByID())
	r.<PERSON>("PUT /news/{id}", handler.UpdateNewsById())
	r.<PERSON>("DELETE /news/{id}", handler.DeleteNewsByID())

	return r
}